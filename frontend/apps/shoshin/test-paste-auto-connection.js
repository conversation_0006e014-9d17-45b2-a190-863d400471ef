// Test script for paste auto-connection functionality
// Run this in the browser console to test paste behavior with auto-connection

console.log("🧪 Testing paste auto-connection functionality...");

// Function to test paste auto-connection
function testPasteAutoConnection() {
  // Get the editor store
  const editorStore = window.useEditorStore?.getState();
  if (!editorStore) {
    console.error("❌ Editor store not found");
    return;
  }

  console.log("📋 Current state:", {
    nodesCount: editorStore.nodes.length,
    edgesCount: editorStore.edges.length,
    clipboardData: editorStore.clipboardData,
  });

  // Test 1: Create initial nodes for testing
  console.log("\n=== Test 1: Setting up test environment ===");
  
  // Clear existing nodes and edges
  editorStore.setNodes([]);
  editorStore.setEdges([]);
  
  // Create a start node
  const startNode = {
    id: 'start-node-test',
    type: 'custom',
    position: { x: 100, y: 100 },
    data: {
      type: 'start',
      label: 'Start Node',
      description: 'Starting point for workflow'
    },
    selected: false
  };

  // Create an LLM node
  const llmNode = {
    id: 'llm-node-test',
    type: 'custom',
    position: { x: 400, y: 100 },
    data: {
      type: 'agent',
      label: 'LLM Agent',
      description: 'AI processing node'
    },
    selected: false
  };

  // Add the test nodes
  editorStore.addNode(startNode);
  editorStore.addNode(llmNode);
  
  console.log("✅ Created test nodes:", {
    startNode: startNode.data.label,
    llmNode: llmNode.data.label,
  });

  // Test 2: Copy a node and paste it near existing nodes
  console.log("\n=== Test 2: Copy and paste with auto-connection ===");
  
  // Select and copy the LLM node
  editorStore.updateSelection([llmNode], []);
  editorStore.copy();
  
  console.log("📋 Copied LLM node to clipboard");
  
  // Test paste at different positions
  const testPositions = [
    { x: 250, y: 120, description: "Between start and LLM nodes" },
    { x: 550, y: 120, description: "To the right of LLM node" },
    { x: 100, y: 250, description: "Below start node" },
    { x: 1000, y: 500, description: "Far from all nodes (should not auto-connect)" },
  ];

  testPositions.forEach((testPos, index) => {
    console.log(`\n--- Test 2.${index + 1}: Paste at ${testPos.description} ---`);
    console.log(`Position: (${testPos.x}, ${testPos.y})`);
    
    const nodesBefore = editorStore.nodes.length;
    const edgesBefore = editorStore.edges.length;
    
    // Paste at the test position
    editorStore.paste(testPos);
    
    const nodesAfter = editorStore.nodes.length;
    const edgesAfter = editorStore.edges.length;
    
    console.log(`Nodes: ${nodesBefore} → ${nodesAfter} (+${nodesAfter - nodesBefore})`);
    console.log(`Edges: ${edgesBefore} → ${edgesAfter} (+${edgesAfter - edgesBefore})`);
    
    if (edgesAfter > edgesBefore) {
      const newEdge = editorStore.edges[editorStore.edges.length - 1];
      const sourceNode = editorStore.nodes.find(n => n.id === newEdge.source);
      const targetNode = editorStore.nodes.find(n => n.id === newEdge.target);
      console.log(`🔗 Auto-connection created: "${sourceNode?.data.label}" → "${targetNode?.data.label}"`);
    } else {
      console.log(`ℹ️ No auto-connection created (expected for far positions)`);
    }
  });

  // Test 3: Test paste with multiple nodes (should not auto-connect)
  console.log("\n=== Test 3: Copy multiple nodes and paste (should not auto-connect) ===");
  
  // Select both nodes and copy them
  editorStore.updateSelection([startNode, llmNode], []);
  editorStore.copy();
  
  console.log("📋 Copied multiple nodes to clipboard");
  
  const nodesBefore = editorStore.nodes.length;
  const edgesBefore = editorStore.edges.length;
  
  // Paste at a position near existing nodes
  editorStore.paste({ x: 200, y: 300 });
  
  const nodesAfter = editorStore.nodes.length;
  const edgesAfter = editorStore.edges.length;
  
  console.log(`Nodes: ${nodesBefore} → ${nodesAfter} (+${nodesAfter - nodesBefore})`);
  console.log(`Edges: ${edgesBefore} → ${edgesAfter} (+${edgesAfter - edgesBefore})`);
  console.log("ℹ️ Multiple nodes pasted - auto-connection should be skipped");

  // Test 4: Test paste on empty canvas (should not auto-connect)
  console.log("\n=== Test 4: Paste on empty canvas (should not auto-connect) ===");
  
  // Clear all nodes and edges
  editorStore.setNodes([]);
  editorStore.setEdges([]);
  
  // Copy a single node to clipboard manually
  editorStore.clipboardData = {
    nodes: [{
      id: 'test-single-node',
      type: 'custom',
      position: { x: 0, y: 0 },
      data: {
        type: 'function',
        label: 'Test Function',
        description: 'A test function node'
      },
      selected: false
    }],
    edges: [],
    timestamp: Date.now()
  };
  
  const nodesBefore4 = editorStore.nodes.length;
  const edgesBefore4 = editorStore.edges.length;
  
  // Paste on empty canvas
  editorStore.paste({ x: 300, y: 200 });
  
  const nodesAfter4 = editorStore.nodes.length;
  const edgesAfter4 = editorStore.edges.length;
  
  console.log(`Nodes: ${nodesBefore4} → ${nodesAfter4} (+${nodesAfter4 - nodesBefore4})`);
  console.log(`Edges: ${edgesBefore4} → ${edgesAfter4} (+${edgesAfter4 - edgesBefore4})`);
  console.log("ℹ️ Empty canvas - auto-connection should be skipped");

  console.log("\n=== Test Complete ===");
  console.log("🎯 Final state:", {
    totalNodes: editorStore.nodes.length,
    totalEdges: editorStore.edges.length,
  });
}

// Run the test
testPasteAutoConnection();
