"use client";

import { create } from "zustand";
import { persist } from "zustand/middleware";

interface EditorSettings {
  // Minimap settings
  showMinimap: boolean;
  
  // Canvas settings
  showCanvasDots: boolean;
}

interface SettingsState {
  // Editor settings
  editor: EditorSettings;
  
  // Actions
  setEditorSetting: <K extends keyof EditorSettings>(
    key: K,
    value: EditorSettings[K]
  ) => void;
  toggleMinimap: () => void;
  toggleCanvasDots: () => void;
  resetEditorSettings: () => void;
}

const defaultEditorSettings: EditorSettings = {
  showMinimap: true,
  showCanvasDots: true,
};

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      // Initial state
      editor: defaultEditorSettings,

      // Actions
      setEditorSetting: (key, value) => {
        set((state) => ({
          editor: {
            ...state.editor,
            [key]: value,
          },
        }));
      },

      toggleMinimap: () => {
        set((state) => ({
          editor: {
            ...state.editor,
            showMinimap: !state.editor.showMinimap,
          },
        }));
      },

      toggleCanvasDots: () => {
        set((state) => ({
          editor: {
            ...state.editor,
            showCanvasDots: !state.editor.showCanvasDots,
          },
        }));
      },

      resetEditorSettings: () => {
        set({
          editor: { ...defaultEditorSettings },
        });
      },
    }),
    {
      name: "shoshin-settings",
      partialize: (state) => ({ editor: state.editor }),
    }
  )
);

// Expose store to window for debugging (development only)
if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
  (window as any).useSettingsStore = useSettingsStore;
}
