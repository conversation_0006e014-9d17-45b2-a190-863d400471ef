# Auto-Connection Feature

## Overview

The Shoshin editor includes an auto-connection feature that automatically creates edges between nodes in two scenarios:

1. **Drop Auto-Connection**: When you drop a new node onto the canvas from the sidebar
2. **Paste Auto-Connection**: When you paste a single node using Ctrl+V or the paste function

This feature improves workflow efficiency by automatically connecting new nodes to the nearest existing node.

## How It Works

### Drop Auto-Connection

When you drag and drop a new node from the sidebar onto the canvas:

1. **Find Nearest Node**: The system calculates the distance from the new node's position to all existing nodes on the canvas
2. **Distance Check**: Only nodes within 800 pixels are considered for auto-connection (to avoid connecting nodes that are too far apart)
3. **Auto-Connect**: If a suitable node is found, an edge is automatically created from the nearest node's exit point (right side) to the new node's entry point (left side)
4. **Logging**: The system logs the auto-connection activity to the browser console for debugging

### Paste Auto-Connection

When you paste a single node using Ctrl+V or the paste function:

1. **Single Node Check**: Auto-connection only occurs when pasting a single node (multiple nodes are skipped)
2. **Find Nearest Node**: The system calculates the distance from the pasted node's position to all existing nodes on the canvas
3. **Distance Check**: Only nodes within 800 pixels are considered for auto-connection
4. **Auto-Connect**: If a suitable node is found, an edge is automatically created from the nearest node's exit point to the pasted node's entry point
5. **Position-Based**: The pasted node is positioned at the cursor location or last known cursor position
6. **Logging**: The system logs the auto-connection activity to the browser console for debugging

## Features

### Distance Threshold
- **Maximum Distance**: 800 pixels
- **Purpose**: Prevents auto-connection when nodes are too far apart, which would create confusing long edges
- **Behavior**: If no nodes are within 800px, no auto-connection occurs

### Connection Direction
- **Source**: Exit point (right side) of the nearest existing node
- **Target**: Entry point (left side) of the newly dropped node
- **Edge Type**: Uses the "shoshin" edge type with animation enabled

### Visual Feedback
- **Console Logging**: Auto-connection events are logged to the browser console
- **Edge Animation**: Auto-created edges have animation enabled for visual feedback
- **Edge Styling**: Uses the same styling as manually created edges

## Implementation Details

### Code Location

#### Drop Auto-Connection
- **Main Logic**: `frontend/apps/shoshin/components/editor/MainCanvas.tsx`
- **Function**: `findClosestNode()` and `onDrop()` callback
- **Store Integration**: Uses `connectNodes()` from the editor store

#### Paste Auto-Connection
- **Main Logic**: `frontend/apps/shoshin/stores/editorStore.ts`
- **Function**: `paste()` method with embedded `findClosestNode()` helper
- **Store Integration**: Direct edge creation in the store state

### Algorithm
```javascript
// 1. Calculate distances to all existing nodes
const existingNodes = nodes
  .map((node) => ({
    ...node,
    distance: Math.sqrt(
      (node.position.x - newNodePosition.x) ** 2 +
        (node.position.y - newNodePosition.y) ** 2
    ),
  }))
  // 2. Filter by distance threshold
  .filter((node) => node.distance <= MAX_AUTO_CONNECT_DISTANCE)
  // 3. Sort by distance (closest first)
  .sort((a, b) => a.distance - b.distance)

// 4. Return closest node or null
return existingNodes[0] || null
```

### Edge Creation
```javascript
const autoConnectEdge = {
  id: `edge-${closestNode.id}-${newNode.id}`,
  source: closestNode.id,
  target: newNode.id,
  type: "shoshin",
  animated: true,
}
```

## User Experience

### When Auto-Connection Occurs
- ✅ **Drop**: Dropping a node near existing nodes (within 800px)
- ✅ **Paste**: Pasting a single node near existing nodes (within 800px)
- ✅ Canvas has at least one existing node
- ✅ Nearest node is within the distance threshold

### When Auto-Connection is Skipped
- ❌ No existing nodes on the canvas
- ❌ All existing nodes are more than 800px away
- ❌ **Paste Only**: Pasting multiple nodes at once
- ❌ System errors or invalid node data

### Console Messages

#### Drop Auto-Connection
- `🔗 Auto-connecting new node "NodeName" to closest node "ClosestNodeName"`
- `ℹ️ No suitable node found for auto-connection (all nodes too far from new position)`
- `ℹ️ No existing nodes to connect to`

#### Paste Auto-Connection
- `🔗 Auto-connecting pasted node "NodeName" to closest node "ClosestNodeName"`
- `ℹ️ No suitable node found for auto-connection (all nodes too far from pasted position)`
- `ℹ️ No existing nodes to connect pasted node to`
- `ℹ️ Auto-connection skipped for multiple pasted nodes (X nodes)`

## Testing

### Test Coverage
The auto-connection feature includes comprehensive tests:

1. **Unit Tests**: `lib/test-auto-connection.ts`
2. **Distance Calculations**: Verifies correct nearest node detection
3. **Threshold Testing**: Confirms 800px distance limit works
4. **Edge Cases**: Tests empty canvas and far-away drops

### Running Tests
```bash
cd frontend/apps/shoshin
node lib/test-auto-connection.ts
```

## Configuration

### Customizable Parameters
- **MAX_AUTO_CONNECT_DISTANCE**: Currently set to 800px
- **Edge Type**: Currently uses "shoshin" type
- **Animation**: Currently enabled by default

### Future Enhancements
- User preference to enable/disable auto-connection
- Configurable distance threshold
- Smart connection based on node types
- Multiple connection options when multiple nodes are equally close

## Compatibility

### ReactFlow Integration
- Compatible with ReactFlow's drag and drop system
- Uses ReactFlow's edge creation and management
- Integrates with existing node and edge types

### Store Integration
- Uses Zustand editor store for state management
- Maintains undo/redo functionality
- Preserves existing edge management features

## Troubleshooting

### Common Issues
1. **No Auto-Connection**: Check console for distance messages
2. **Unexpected Connections**: Verify node positions and distance calculations
3. **Performance**: Large numbers of nodes may slow distance calculations

### Debug Information
Enable browser console to see auto-connection logging and troubleshoot issues.
