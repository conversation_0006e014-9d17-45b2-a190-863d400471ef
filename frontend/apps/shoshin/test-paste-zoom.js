// Test script to verify paste functionality works correctly at different zoom levels
// Run this in the browser console when the Shoshin editor is open

console.log("🧪 Starting paste zoom test...");

// Get the editor store
const editorStore = window.editorStore || (() => {
  console.error("❌ Editor store not found. Make sure you're on the editor page.");
  return null;
})();

if (!editorStore) {
  console.log("❌ Test aborted: Editor store not available");
} else {
  console.log("✅ Editor store found");
}

// Get ReactFlow instance
const reactFlowInstance = window.__reactFlowInstance;
if (!reactFlowInstance) {
  console.log("❌ ReactFlow instance not found");
} else {
  console.log("✅ ReactFlow instance found");
}

// Test function to simulate paste at different zoom levels
async function testPasteAtZoom(zoomLevel, testName) {
  console.log(`\n🔍 Testing paste at zoom level ${zoomLevel} (${testName})`);
  
  // Set zoom level
  if (reactFlowInstance && reactFlowInstance.setViewport) {
    reactFlowInstance.setViewport({ x: 0, y: 0, zoom: zoomLevel });
    console.log(`📏 Set zoom to ${zoomLevel}`);
  }
  
  // Wait a bit for zoom to settle
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // Create some test nodes to copy
  const testNode = {
    id: `test-node-${Date.now()}`,
    type: "custom",
    position: { x: 100, y: 100 },
    data: {
      label: `Test Node (zoom ${zoomLevel})`,
      type: "test",
      description: "Test node for paste functionality"
    }
  };
  
  // Add the test node
  editorStore.addNode(testNode);
  console.log("➕ Added test node");
  
  // Select and copy the node
  editorStore.updateSelection([testNode], []);
  editorStore.copy();
  console.log("📋 Copied test node");
  
  // Simulate mouse position at center of viewport
  const reactFlowWrapper = document.querySelector('[data-testid="rf__wrapper"]');
  if (reactFlowWrapper) {
    const bounds = reactFlowWrapper.getBoundingClientRect();
    const centerX = bounds.left + bounds.width / 2;
    const centerY = bounds.top + bounds.height / 2;
    
    // Update global mouse position
    window.__lastMousePosition = { x: centerX, y: centerY };
    console.log(`🖱️ Set mouse position to center: (${centerX}, ${centerY})`);
  }
  
  // Paste the node
  editorStore.paste();
  console.log("📌 Pasted node");
  
  // Get the pasted node position
  const nodes = editorStore.nodes;
  const pastedNode = nodes[nodes.length - 1]; // Should be the last added node
  
  if (pastedNode) {
    console.log(`✅ Pasted node position: (${pastedNode.position.x}, ${pastedNode.position.y})`);
    
    // Check if the position is reasonable (should be near the center of the viewport)
    if (reactFlowInstance && reactFlowInstance.screenToFlowPosition) {
      const expectedPosition = reactFlowInstance.screenToFlowPosition({
        x: reactFlowWrapper.getBoundingClientRect().width / 2,
        y: reactFlowWrapper.getBoundingClientRect().height / 2,
      });
      
      const distance = Math.sqrt(
        Math.pow(pastedNode.position.x - expectedPosition.x, 2) +
        Math.pow(pastedNode.position.y - expectedPosition.y, 2)
      );
      
      console.log(`📐 Expected position: (${expectedPosition.x}, ${expectedPosition.y})`);
      console.log(`📏 Distance from expected: ${distance.toFixed(2)} pixels`);
      
      if (distance < 50) {
        console.log(`✅ ${testName}: PASS - Node pasted close to expected position`);
      } else {
        console.log(`❌ ${testName}: FAIL - Node pasted far from expected position`);
      }
    }
  } else {
    console.log(`❌ ${testName}: FAIL - No pasted node found`);
  }
}

// Run tests at different zoom levels
async function runAllTests() {
  if (!editorStore || !reactFlowInstance) {
    console.log("❌ Cannot run tests: Missing dependencies");
    return;
  }
  
  console.log("🚀 Running paste zoom tests...");
  
  // Test at different zoom levels
  await testPasteAtZoom(0.5, "Zoomed Out");
  await testPasteAtZoom(1.0, "Normal Zoom");
  await testPasteAtZoom(1.5, "Zoomed In");
  await testPasteAtZoom(2.0, "Very Zoomed In");
  
  console.log("\n🏁 All tests completed!");
  console.log("Check the results above to see if paste works correctly at all zoom levels.");
}

// Export the test function for manual use
window.testPasteZoom = runAllTests;

// Auto-run the tests
runAllTests();
