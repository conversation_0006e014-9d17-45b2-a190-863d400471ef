"use client"

import { useThemeStore } from '@/stores/themeStore'
import { useEffect } from 'react'

interface ThemeProviderProps {
  children: React.ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const initializeTheme = useThemeStore((state) => state.initializeTheme)
  const isLoading = useThemeStore((state) => state.isLoading)

  useEffect(() => {
    // Initialize theme on mount
    initializeTheme()
  }, [initializeTheme])

  // Prevent flash of unstyled content by not rendering until theme is initialized
  // Add timeout to prevent infinite loading
  if (isLoading) {
    // Set a fallback timeout to prevent infinite loading
    setTimeout(() => {
      if (useThemeStore.getState().isLoading) {
        useThemeStore.setState({ isLoading: false })
      }
    }, 1000)

    return (
      <div className="fixed inset-0 flex items-center justify-center" style={{ backgroundColor: 'white' }}>
        <div className="w-8 h-8 border-2 border-gray-200 border-t-purple-500 rounded-full animate-spin"></div>
      </div>
    )
  }

  return <>{children}</>
}
