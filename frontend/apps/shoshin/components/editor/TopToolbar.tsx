"use client"

import { SettingsModal } from "@/components/settings/SettingsModal"
import { Button } from "@/components/ui/button"
import {
    MessageSquare,
    MoreHorizontal,
    Network,
    Play,
    Save,
    Search,
    Settings,
    Share2,
    User
} from "lucide-react"
import { useEffect, useState } from "react"

export function TopToolbar() {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [isLayouting, setIsLayouting] = useState(false)

  const handleAutoLayout = () => {
    setIsLayouting(true)
    window.dispatchEvent(new CustomEvent('trigger-auto-layout'))

    // Reset the loading state after animation duration
    setTimeout(() => {
      setIsLayouting(false)
    }, 1000) // Slightly longer than animation duration for better UX

    console.log('Auto Layout triggered')
  }

  const handleSettingsClick = () => {
    setIsSettingsOpen(true)
  }

  // Keyboard shortcut for Auto Layout (Shift+L)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.shiftKey && event.key.toLowerCase() === 'l') {
        event.preventDefault()
        handleAutoLayout()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [])
  return (
    <div className="h-16 bg-background border-b border-border flex items-center px-6 justify-between" style={{ paddingLeft: '96px' }}>
      {/* Left Section */}
      <div className="flex items-center space-x-4">
        {/* User Avatar */}
        <div className="w-10 h-10 bg-primary-500 rounded-md flex items-center justify-center">
          <User className="w-5 h-5 text-white" />
        </div>

        {/* Project Info */}
        <div className="text-foreground">
          <div className="text-sm font-semibold text-foreground">Creative Project</div>
          <div className="text-xs text-muted-foreground">Saved 16 hours ago</div>
        </div>
      </div>

      {/* Center Section - Search */}
      <div className="flex-1 max-w-md mx-8">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search nodes, blocks..."
            className="w-full bg-neutral-100 dark:bg-neutral-800 text-foreground placeholder-muted-foreground pl-10 pr-4 py-2 rounded-md border border-border focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500/20 transition-all duration-200"
          />
        </div>
      </div>

      {/* Right Section */}
      <div className="flex items-center space-x-3">
        <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
          <Save className="w-4 h-4" />
        </Button>
        <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
          <Share2 className="w-4 h-4" />
        </Button>
        <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
          <MessageSquare className="w-4 h-4" />
        </Button>
        <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground" onClick={handleSettingsClick}>
          <Settings className="w-4 h-4" />
        </Button>

        {/* Auto Layout Button */}
        <Button
          variant="outline"
          size="sm"
          className="border-primary-500/30 text-primary-500 hover:bg-primary-50 dark:hover:bg-primary-950"
          title="Auto Layout (Shift+L)"
          onClick={handleAutoLayout}
          disabled={isLayouting}
        >
          <Network className={`w-4 h-4 mr-2 ${isLayouting ? 'animate-spin' : ''}`} />
          {isLayouting ? 'Layouting...' : 'Auto Layout'}
        </Button>

        <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
          <MoreHorizontal className="w-4 h-4" />
        </Button>

        {/* Debug Button */}
        <Button
          size="sm"
          className="px-4"
        >
          <Play className="w-4 h-4 mr-2" />
          Debug
        </Button>
      </div>

      {/* Settings Modal */}
      <SettingsModal
        open={isSettingsOpen}
        onOpenChange={setIsSettingsOpen}
      />
    </div>
  )
}
