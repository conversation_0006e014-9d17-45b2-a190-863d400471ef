// Test script for paste functionality
// Run this in the browser console to test paste behavior

console.log("🧪 Testing paste functionality...");

// Function to simulate adding a node and copying it
function testPasteAtCursor() {
  // Get the editor store
  const editorStore = window.useEditorStore?.getState();
  if (!editorStore) {
    console.error("❌ Editor store not found");
    return;
  }

  console.log("📋 Current clipboard data:", editorStore.clipboardData);
  console.log("🖱️ Current cursor position:", editorStore.lastCursorPosition);

  // If no clipboard data, create a test node first
  if (!editorStore.clipboardData || editorStore.clipboardData.nodes.length === 0) {
    console.log("📝 Creating test node for clipboard...");
    
    const testNode = {
      id: 'test-node-' + Date.now(),
      type: 'custom',
      position: { x: 100, y: 100 },
      data: {
        type: 'input',
        label: 'Test Node',
        description: 'A test node for paste functionality'
      },
      selected: true
    };

    // Add the test node
    editorStore.addNode(testNode);
    
    // Select and copy it
    editorStore.setNodes([...editorStore.nodes]);
    editorStore.updateSelection([testNode], []);
    editorStore.copy();
    
    console.log("✅ Test node created and copied");
  }

  // Now test paste at different positions
  console.log("🎯 Testing paste at cursor position...");
  
  // Test 1: Paste without explicit position (should use cursor position)
  editorStore.paste();
  
  // Test 2: Paste at specific position
  setTimeout(() => {
    console.log("🎯 Testing paste at specific position (500, 200)...");
    editorStore.paste({ x: 500, y: 200 });
  }, 1000);

  // Test 3: Update cursor position and paste again
  setTimeout(() => {
    console.log("🎯 Updating cursor position to (300, 400) and pasting...");
    editorStore.updateCursorPosition({ x: 300, y: 400 });
    editorStore.paste();
  }, 2000);
}

// Function to test mouse tracking
function testMouseTracking() {
  console.log("🖱️ Testing mouse position tracking...");
  console.log("Move your mouse around the canvas and watch the console for position updates");
  
  // Log current cursor position every 2 seconds
  const interval = setInterval(() => {
    const editorStore = window.useEditorStore?.getState();
    if (editorStore) {
      console.log("📍 Current cursor position:", editorStore.lastCursorPosition);
    }
  }, 2000);

  // Stop logging after 10 seconds
  setTimeout(() => {
    clearInterval(interval);
    console.log("🛑 Mouse tracking test completed");
  }, 10000);
}

// Export functions to global scope for easy testing
window.testPasteAtCursor = testPasteAtCursor;
window.testMouseTracking = testMouseTracking;

console.log("🚀 Test functions ready!");
console.log("Run testPasteAtCursor() to test paste functionality");
console.log("Run testMouseTracking() to test mouse position tracking");
