import { calculateAutoLayout } from './autoLayout'
import type { Node, Edge } from '@xyflow/react'

// Helper function to create test nodes
const createNode = (id: string): Node => ({
  id,
  type: 'custom',
  position: { x: 0, y: 0 },
  data: { label: `Node ${id}` }
})

// Helper function to create test edges
const createEdge = (source: string, target: string): Edge => ({
  id: `${source}-${target}`,
  source,
  target
})

// Test scenario that specifically creates edge-node overlap potential
console.log('=== Edge-Node Overlap Prevention Test ===')

// Create a scenario where node C would naturally be positioned in the path of edge A->B
const testNodes: Node[] = [
  createNode('A'),
  createNode('B'), 
  createNode('C'), // This node should be moved away from A->B edge
  createNode('D')
]

const testEdges: Edge[] = [
  createEdge('A', 'B'), // Main edge that C might overlap with
  createEdge('C', 'D')  // Secondary edge
]

// Test with very tight spacing to force overlaps
const result = calculateAutoLayout(testNodes, testEdges, {
  edgeNodeSpacing: 20,
  edgeNodeCollisionIterations: 10,
  horizontalSpacing: 100, // Tight spacing
  verticalSpacing: 80,    // Tight spacing
  minimizeEdgeCrossings: true,
  dynamicSpacing: true
})

console.log('Final node positions:')
result.positions.forEach((pos, nodeId) => {
  console.log(`  ${nodeId}: (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)})`)
})

// Verify that nodes are properly spaced
const positions = Array.from(result.positions.entries())
console.log('\nNode-to-node distances:')
for (let i = 0; i < positions.length; i++) {
  for (let j = i + 1; j < positions.length; j++) {
    const [nodeId1, pos1] = positions[i]
    const [nodeId2, pos2] = positions[j]
    
    const distance = Math.sqrt(
      Math.pow(pos2.x - pos1.x, 2) + Math.pow(pos2.y - pos1.y, 2)
    )
    
    console.log(`  ${nodeId1} to ${nodeId2}: ${distance.toFixed(1)}px`)
  }
}

// Test with even more aggressive spacing
console.log('\n=== Stress Test with Very Tight Spacing ===')

const stressResult = calculateAutoLayout(testNodes, testEdges, {
  edgeNodeSpacing: 25, // Even more aggressive
  edgeNodeCollisionIterations: 15,
  horizontalSpacing: 60,  // Very tight
  verticalSpacing: 60,    // Very tight
  minimizeEdgeCrossings: true,
  dynamicSpacing: true
})

console.log('Stress test positions:')
stressResult.positions.forEach((pos, nodeId) => {
  console.log(`  ${nodeId}: (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)})`)
})

const stressPositions = Array.from(stressResult.positions.entries())
let minDistance = Infinity
let minPair = ''

for (let i = 0; i < stressPositions.length; i++) {
  for (let j = i + 1; j < stressPositions.length; j++) {
    const [nodeId1, pos1] = stressPositions[i]
    const [nodeId2, pos2] = stressPositions[j]
    
    const distance = Math.sqrt(
      Math.pow(pos2.x - pos1.x, 2) + Math.pow(pos2.y - pos1.y, 2)
    )
    
    if (distance < minDistance) {
      minDistance = distance
      minPair = `${nodeId1}-${nodeId2}`
    }
  }
}

console.log(`\nMinimum distance found: ${minDistance.toFixed(1)}px between ${minPair}`)
console.log(`Spacing requirement met: ${minDistance >= 25 ? 'YES ✅' : 'NO ❌'}`)

console.log('\n=== Edge Overlap Test Completed ===')
