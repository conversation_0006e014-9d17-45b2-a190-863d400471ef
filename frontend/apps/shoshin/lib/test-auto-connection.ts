/**
 * Test script to verify auto-connection functionality
 */

// Mock nodes for testing
const testNodes = [
  {
    id: "start-1",
    type: "custom",
    position: { x: 100, y: 100 },
    data: {
      label: "Start",
      type: "start",
      description: "Starting node",
    },
  },
  {
    id: "llm-1",
    type: "custom",
    position: { x: 400, y: 100 },
    data: {
      label: "LLM",
      type: "llm",
      description: "Language model",
    },
  },
  {
    id: "knowledge-1",
    type: "custom",
    position: { x: 700, y: 100 },
    data: {
      label: "Knowledge",
      type: "knowledge",
      description: "Knowledge base",
    },
  },
];

// Helper function to find the closest node to a given position
const findClosestNode = (newNodePosition, nodes) => {
  const MAX_AUTO_CONNECT_DISTANCE = 800; // Maximum distance for auto-connection

  const existingNodes = nodes
    .map((node) => ({
      ...node,
      distance: Math.sqrt(
        (node.position.x - newNodePosition.x) ** 2 +
          (node.position.y - newNodePosition.y) ** 2,
      ),
    }))
    .filter((node) => node.distance <= MAX_AUTO_CONNECT_DISTANCE) // Only consider nodes within reasonable distance
    .sort((a, b) => a.distance - b.distance);

  return existingNodes[0] || null;
};

// Test cases
console.log("=== Auto-Connection Test Cases ===");

// Test 1: Drop node near start node
console.log("\nTest 1: Drop node near start node");
const newPosition1 = { x: 250, y: 120 }; // Between start and llm
const closest1 = findClosestNode(newPosition1, testNodes);
console.log(`New node position: (${newPosition1.x}, ${newPosition1.y})`);
console.log(
  `Closest node: ${closest1?.data.label} at (${closest1?.position.x}, ${closest1?.position.y})`,
);
console.log(
  `Distance: ${Math.sqrt((closest1!.position.x - newPosition1.x) ** 2 + (closest1!.position.y - newPosition1.y) ** 2).toFixed(2)}px`,
);

// Test 2: Drop node near knowledge node
console.log("\nTest 2: Drop node near knowledge node");
const newPosition2 = { x: 750, y: 200 }; // Near knowledge node
const closest2 = findClosestNode(newPosition2, testNodes);
console.log(`New node position: (${newPosition2.x}, ${newPosition2.y})`);
console.log(
  `Closest node: ${closest2?.data.label} at (${closest2?.position.x}, ${closest2?.position.y})`,
);
console.log(
  `Distance: ${Math.sqrt((closest2!.position.x - newPosition2.x) ** 2 + (closest2!.position.y - newPosition2.y) ** 2).toFixed(2)}px`,
);

// Test 3: Drop node far from all nodes
console.log("\nTest 3: Drop node far from all nodes");
const newPosition3 = { x: 1000, y: 500 }; // Far from all nodes
const closest3 = findClosestNode(newPosition3, testNodes);
console.log(`New node position: (${newPosition3.x}, ${newPosition3.y})`);
if (closest3) {
  console.log(
    `Closest node: ${closest3.data.label} at (${closest3.position.x}, ${closest3.position.y})`,
  );
  console.log(
    `Distance: ${Math.sqrt((closest3.position.x - newPosition3.x) ** 2 + (closest3.position.y - newPosition3.y) ** 2).toFixed(2)}px`,
  );
} else {
  console.log(`No node found within auto-connect distance (800px)`);
}

// Test 4: Drop node in center of existing nodes
console.log("\nTest 4: Drop node in center of existing nodes");
const newPosition4 = { x: 400, y: 200 }; // Center area
const closest4 = findClosestNode(newPosition4, testNodes);
console.log(`New node position: (${newPosition4.x}, ${newPosition4.y})`);
console.log(
  `Closest node: ${closest4?.data.label} at (${closest4?.position.x}, ${closest4?.position.y})`,
);
console.log(
  `Distance: ${Math.sqrt((closest4!.position.x - newPosition4.x) ** 2 + (closest4!.position.y - newPosition4.y) ** 2).toFixed(2)}px`,
);

// Test 5: Drop node beyond auto-connect distance
console.log("\nTest 5: Drop node beyond auto-connect distance");
const newPosition5 = { x: 1200, y: 800 }; // Very far from all nodes
const closest5 = findClosestNode(newPosition5, testNodes);
console.log(`New node position: (${newPosition5.x}, ${newPosition5.y})`);
if (closest5) {
  console.log(
    `Closest node: ${closest5.data.label} at (${closest5.position.x}, ${closest5.position.y})`,
  );
  console.log(
    `Distance: ${Math.sqrt((closest5.position.x - newPosition5.x) ** 2 + (closest5.position.y - newPosition5.y) ** 2).toFixed(2)}px`,
  );
} else {
  console.log(
    `No node found within auto-connect distance (800px) - auto-connection will be skipped`,
  );
}

console.log("\n=== Test Complete ===");
