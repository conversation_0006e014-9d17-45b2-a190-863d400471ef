import { type Edge, type Node } from "@xyflow/react";
import { calculateAutoLayout } from "./autoLayout";

// Helper functions for creating test nodes and edges
const createNode = (id: string, x: number = 0, y: number = 0): Node => ({
  id,
  type: "custom",
  position: { x, y },
  data: { label: id },
});

const createEdge = (source: string, target: string): Edge => ({
  id: `${source}-${target}`,
  source,
  target,
  type: "shoshin",
});

console.log("=== Direct Edge-to-Edge Distance Test ===");

// Create a scenario where edges are forced to be very close
// Two parallel horizontal edges that are only 5px apart vertically
const testNodes: Node[] = [
  createNode("A", 0, 0),    // Top-left
  createNode("B", 100, 0),  // Top-right
  createNode("C", 0, 5),    // Bottom-left (only 5px below A)
  createNode("D", 100, 5),  // Bottom-right (only 5px below B)
];

const testEdges: Edge[] = [
  createEdge("A", "B"), // Top horizontal edge
  createEdge("C", "D"), // Bottom horizontal edge (very close to top edge)
];

console.log("Initial node positions (before auto-layout):");
testNodes.forEach(node => {
  console.log(`  ${node.id}: (${node.position.x}, ${node.position.y})`);
});

// Test with edge-edge spacing enabled
console.log("\n=== Testing with Edge-Edge Spacing Enabled ===");
const resultWithSpacing = calculateAutoLayout(testNodes, testEdges, {
  edgeNodeSpacing: 0, // Disable edge-node spacing to focus on edge-edge
  edgeNodeCollisionIterations: 0,
  edgeEdgeSpacing: 10, // Minimum 10px between edges
  edgeEdgeCollisionIterations: 10, // More iterations to ensure convergence
  horizontalSpacing: 100, // Keep horizontal spacing as is
  verticalSpacing: 5, // Very small vertical spacing to force close edges
  minimizeEdgeCrossings: false, // Disable to avoid interference
  dynamicSpacing: false, // Disable to avoid interference
});

console.log("Node positions with edge-edge spacing:");
resultWithSpacing.positions.forEach((pos, nodeId) => {
  console.log(`  ${nodeId}: (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)})`);
});

// Calculate actual edge distance
const posA = resultWithSpacing.positions.get("A")!;
const posB = resultWithSpacing.positions.get("B")!;
const posC = resultWithSpacing.positions.get("C")!;
const posD = resultWithSpacing.positions.get("D")!;

const edge1CenterY = (posA.y + posB.y) / 2;
const edge2CenterY = (posC.y + posD.y) / 2;
const edgeDistance = Math.abs(edge2CenterY - edge1CenterY);

console.log(`\nEdge distance analysis:`);
console.log(`  Edge A-B center Y: ${edge1CenterY.toFixed(1)}`);
console.log(`  Edge C-D center Y: ${edge2CenterY.toFixed(1)}`);
console.log(`  Distance between edges: ${edgeDistance.toFixed(1)}px`);
console.log(`  Minimum spacing requirement met: ${edgeDistance >= 10 ? "YES ✅" : "NO ❌"}`);

// Test without edge-edge spacing for comparison
console.log("\n=== Testing without Edge-Edge Spacing ===");
const resultWithoutSpacing = calculateAutoLayout(testNodes, testEdges, {
  edgeNodeSpacing: 0,
  edgeNodeCollisionIterations: 0,
  edgeEdgeSpacing: 0, // Disabled
  edgeEdgeCollisionIterations: 0,
  horizontalSpacing: 100,
  verticalSpacing: 5,
  minimizeEdgeCrossings: false,
  dynamicSpacing: false,
});

console.log("Node positions without edge-edge spacing:");
resultWithoutSpacing.positions.forEach((pos, nodeId) => {
  console.log(`  ${nodeId}: (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)})`);
});

const posA2 = resultWithoutSpacing.positions.get("A")!;
const posB2 = resultWithoutSpacing.positions.get("B")!;
const posC2 = resultWithoutSpacing.positions.get("C")!;
const posD2 = resultWithoutSpacing.positions.get("D")!;

const edge1CenterY2 = (posA2.y + posB2.y) / 2;
const edge2CenterY2 = (posC2.y + posD2.y) / 2;
const edgeDistance2 = Math.abs(edge2CenterY2 - edge1CenterY2);

console.log(`\nComparison:`);
console.log(`  Distance without spacing: ${edgeDistance2.toFixed(1)}px`);
console.log(`  Distance with spacing: ${edgeDistance.toFixed(1)}px`);
console.log(`  Improvement: ${(edgeDistance - edgeDistance2).toFixed(1)}px`);

if (edgeDistance > edgeDistance2) {
  console.log("✅ Edge-edge spacing is working - edges were pushed apart!");
} else if (edgeDistance === edgeDistance2) {
  console.log("⚠️  No change detected - edges may already be far enough apart");
} else {
  console.log("❌ Unexpected result - spacing may not be working correctly");
}

console.log("\n=== Direct Edge-Edge Distance Test Completed ===");
