import { type Edge, type Node } from "@xyflow/react";
import { calculateAutoLayout } from "./autoLayout";

// Helper functions for creating test nodes and edges
const createNode = (id: string, x: number = 0, y: number = 0): Node => ({
  id,
  type: "custom",
  position: { x, y },
  data: { label: id },
});

const createEdge = (source: string, target: string): Edge => ({
  id: `${source}-${target}`,
  source,
  target,
  type: "shoshin",
});

console.log("=== Improved Edge-Node Collision Detection Test ===");

// Create a scenario similar to the screenshot where edges pass through nodes
const testNodes: Node[] = [
  createNode("A", 0, 0),     // Left node
  createNode("B", 200, 0),   // Right node  
  createNode("C", 100, 10),  // Middle node that could be intersected by A-B edge
  createNode("D", 50, 50),   // Another node that could be intersected
  createNode("E", 150, 50),  // Another node that could be intersected
];

const testEdges: Edge[] = [
  createEdge("A", "B"), // Horizontal edge that might pass through C
  createEdge("C", "D"), // Edge from C to D
  createEdge("C", "E"), // Edge from C to E
];

console.log("Testing improved collision detection with aggressive settings...");

// Test with improved collision detection
const result = calculateAutoLayout(testNodes, testEdges, {
  edgeNodeSpacing: 40, // Large spacing requirement
  edgeNodeCollisionIterations: 15, // Many iterations
  edgeEdgeSpacing: 10,
  edgeEdgeCollisionIterations: 5,
  horizontalSpacing: 150, // Moderate spacing to allow potential overlaps
  verticalSpacing: 80,
  minimizeEdgeCrossings: true,
  dynamicSpacing: true,
});

console.log("Final node positions:");
result.positions.forEach((pos, nodeId) => {
  console.log(`  ${nodeId}: (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)})`);
});

// Verify that nodes are properly separated from edges
const checkEdgeNodeSeparation = (
  nodeId: string,
  edgeSource: string,
  edgeTarget: string,
  positions: Map<string, { x: number; y: number }>
): number => {
  const nodePos = positions.get(nodeId)!;
  const sourcePos = positions.get(edgeSource)!;
  const targetPos = positions.get(edgeTarget)!;
  
  // Simple distance calculation from node center to edge midpoint
  const edgeMidX = (sourcePos.x + targetPos.x) / 2;
  const edgeMidY = (sourcePos.y + targetPos.y) / 2;
  
  const distance = Math.sqrt(
    Math.pow(nodePos.x - edgeMidX, 2) + Math.pow(nodePos.y - edgeMidY, 2)
  );
  
  return distance;
};

console.log("\nEdge-Node separation analysis:");

// Check if node C is properly separated from edge A-B
if (testNodes.find(n => n.id === "C") && testEdges.find(e => e.source === "A" && e.target === "B")) {
  const distance = checkEdgeNodeSeparation("C", "A", "B", result.positions);
  console.log(`  Node C distance from edge A-B: ${distance.toFixed(1)}px`);
  console.log(`  Separation adequate: ${distance >= 40 ? "YES ✅" : "NO ❌"}`);
}

// Check if node D is properly separated from edge A-B
if (testNodes.find(n => n.id === "D") && testEdges.find(e => e.source === "A" && e.target === "B")) {
  const distance = checkEdgeNodeSeparation("D", "A", "B", result.positions);
  console.log(`  Node D distance from edge A-B: ${distance.toFixed(1)}px`);
  console.log(`  Separation adequate: ${distance >= 40 ? "YES ✅" : "NO ❌"}`);
}

// Check if node E is properly separated from edge A-B
if (testNodes.find(n => n.id === "E") && testEdges.find(e => e.source === "A" && e.target === "B")) {
  const distance = checkEdgeNodeSeparation("E", "A", "B", result.positions);
  console.log(`  Node E distance from edge A-B: ${distance.toFixed(1)}px`);
  console.log(`  Separation adequate: ${distance >= 40 ? "YES ✅" : "NO ❌"}`);
}

// Calculate minimum distance between any two nodes
let minNodeDistance = Infinity;
let closestNodePair = "";
const nodePositions = Array.from(result.positions.entries());

for (let i = 0; i < nodePositions.length; i++) {
  for (let j = i + 1; j < nodePositions.length; j++) {
    const [nodeId1, pos1] = nodePositions[i];
    const [nodeId2, pos2] = nodePositions[j];
    
    const distance = Math.sqrt(
      Math.pow(pos2.x - pos1.x, 2) + Math.pow(pos2.y - pos1.y, 2)
    );
    
    if (distance < minNodeDistance) {
      minNodeDistance = distance;
      closestNodePair = `${nodeId1}-${nodeId2}`;
    }
  }
}

console.log(`\nMinimum node-to-node distance: ${minNodeDistance.toFixed(1)}px (${closestNodePair})`);
console.log(`Node spacing adequate: ${minNodeDistance >= 40 ? "YES ✅" : "NO ❌"}`);

console.log("\n=== Improved Collision Detection Test Completed ===");
