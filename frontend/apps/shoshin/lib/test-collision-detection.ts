import type { Edge, Node } from "@xyflow/react";
import { calculateAutoLayout } from "./autoLayout";

// Helper function to create test nodes
const createNode = (id: string): Node => ({
  id,
  type: "custom",
  position: { x: 0, y: 0 },
  data: { label: `Node ${id}` },
});

// Helper function to create test edges
const createEdge = (source: string, target: string): Edge => ({
  id: `${source}-${target}`,
  source,
  target,
});

// Test 1: Node-node collision prevention
console.log("=== Test 1: Node-Node Collision Prevention ===");

const testNodes1: Node[] = [createNode("A"), createNode("B"), createNode("C")];

const testEdges1: Edge[] = [createEdge("A", "B"), createEdge("B", "C")];

const result1 = calculateAutoLayout(testNodes1, testEdges1, {
  edgeNodeSpacing: 20, // Increased spacing for better separation
  edgeNodeCollisionIterations: 8,
  horizontalSpacing: 50, // Small spacing to force potential overlaps
  verticalSpacing: 50,
});

console.log("Node positions:");
result1.positions.forEach((pos, nodeId) => {
  console.log(`  ${nodeId}: (${pos.x}, ${pos.y})`);
});

// Check distances between nodes
const positions1 = Array.from(result1.positions.entries());
console.log("Node distances:");
for (let i = 0; i < positions1.length; i++) {
  for (let j = i + 1; j < positions1.length; j++) {
    const [nodeId1, pos1] = positions1[i];
    const [nodeId2, pos2] = positions1[j];

    const distance = Math.sqrt(
      Math.pow(pos2.x - pos1.x, 2) + Math.pow(pos2.y - pos1.y, 2),
    );

    console.log(`  ${nodeId1} to ${nodeId2}: ${distance.toFixed(2)}px`);
  }
}

// Test 2: Complex graph with edge-node collision prevention
console.log(
  "\n=== Test 2: Complex Graph with Edge-Node Collision Prevention ===",
);

const testNodes2: Node[] = [
  createNode("A"),
  createNode("B"),
  createNode("C"),
  createNode("D"),
  createNode("E"),
  createNode("F"),
];

const testEdges2: Edge[] = [
  createEdge("A", "D"),
  createEdge("B", "E"),
  createEdge("C", "F"),
  createEdge("D", "F"),
  createEdge("E", "F"),
];

const result2 = calculateAutoLayout(testNodes2, testEdges2, {
  edgeNodeSpacing: 20,
  edgeNodeCollisionIterations: 10,
  horizontalSpacing: 150,
  verticalSpacing: 100,
  minimizeEdgeCrossings: true,
  dynamicSpacing: true,
});

console.log("Complex graph node positions:");
result2.positions.forEach((pos, nodeId) => {
  console.log(`  ${nodeId}: (${pos.x}, ${pos.y})`);
});

// Test 3: Minimum spacing verification
console.log("\n=== Test 3: Minimum 10px Spacing Verification ===");

const testNodes3: Node[] = [createNode("X"), createNode("Y")];

const testEdges3: Edge[] = [createEdge("X", "Y")];

const result3 = calculateAutoLayout(testNodes3, testEdges3, {
  edgeNodeSpacing: 20,
  horizontalSpacing: 50, // Force close positioning
  verticalSpacing: 50,
});

const posX = result3.positions.get("X")!;
const posY = result3.positions.get("Y")!;

const distance = Math.sqrt(
  Math.pow(posY.x - posX.x, 2) + Math.pow(posY.y - posY.y, 2),
);

console.log(`Distance between X and Y: ${distance.toFixed(2)}px`);
console.log(`Minimum spacing maintained: ${distance >= 20 ? "YES" : "NO"}`);

console.log("\n=== All Tests Completed ===");
