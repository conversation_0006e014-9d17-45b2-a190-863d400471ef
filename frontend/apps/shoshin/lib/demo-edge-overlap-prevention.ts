import { calculateAutoLayout } from './autoLayout'
import type { Node, Edge } from '@xyflow/react'

// Demo script to show edge overlap prevention in action

// Helper function to create test nodes
const createNode = (id: string): Node => ({
  id,
  type: 'custom',
  position: { x: 0, y: 0 },
  data: { label: `Node ${id}` }
})

// Helper function to create test edges
const createEdge = (source: string, target: string): Edge => ({
  id: `${source}-${target}`,
  source,
  target
})

// Demo 1: Complex graph with potential edge crossings
console.log('=== Demo 1: Complex Graph with Edge Crossing Minimization ===')

const complexNodes: Node[] = [
  createNode('A'),
  createNode('B'),
  createNode('C'),
  createNode('D'),
  createNode('E'),
  createNode('F'),
  createNode('G'),
  createNode('H')
]

const complexEdges: Edge[] = [
  createEdge('A', 'E'),
  createEdge('A', 'F'),
  createEdge('B', 'E'),
  createEdge('B', 'G'),
  createEdge('C', 'F'),
  createEdge('C', 'G'),
  createEdge('D', 'H'),
  createEdge('E', 'H'),
  createEdge('F', 'H'),
  createEdge('G', 'H')
]

// Without edge crossing minimization
const resultWithoutOptimization = calculateAutoLayout(complexNodes, complexEdges, {
  minimizeEdgeCrossings: false,
  dynamicSpacing: false,
  horizontalSpacing: 300,
  verticalSpacing: 200
})

console.log('Without optimization:')
resultWithoutOptimization.positions.forEach((pos, nodeId) => {
  console.log(`  ${nodeId}: (${pos.x}, ${pos.y})`)
})

// With edge crossing minimization
const resultWithOptimization = calculateAutoLayout(complexNodes, complexEdges, {
  minimizeEdgeCrossings: true,
  edgeCrossingIterations: 3,
  dynamicSpacing: true,
  horizontalSpacing: 300,
  verticalSpacing: 200
})

console.log('\nWith edge crossing minimization:')
resultWithOptimization.positions.forEach((pos, nodeId) => {
  console.log(`  ${nodeId}: (${pos.x}, ${pos.y})`)
})

// Demo 2: High edge density graph
console.log('\n=== Demo 2: High Edge Density with Dynamic Spacing ===')

const denseNodes: Node[] = [
  createNode('X1'),
  createNode('X2'),
  createNode('X3'),
  createNode('Y1'),
  createNode('Y2'),
  createNode('Y3'),
  createNode('Y4')
]

const denseEdges: Edge[] = [
  createEdge('X1', 'Y1'),
  createEdge('X1', 'Y2'),
  createEdge('X1', 'Y3'),
  createEdge('X1', 'Y4'),
  createEdge('X2', 'Y1'),
  createEdge('X2', 'Y2'),
  createEdge('X2', 'Y3'),
  createEdge('X2', 'Y4'),
  createEdge('X3', 'Y1'),
  createEdge('X3', 'Y2'),
  createEdge('X3', 'Y3'),
  createEdge('X3', 'Y4')
]

// Without dynamic spacing
const resultWithoutDynamicSpacing = calculateAutoLayout(denseNodes, denseEdges, {
  dynamicSpacing: false,
  horizontalSpacing: 300,
  verticalSpacing: 200
})

console.log('Without dynamic spacing:')
resultWithoutDynamicSpacing.positions.forEach((pos, nodeId) => {
  console.log(`  ${nodeId}: (${pos.x}, ${pos.y})`)
})

// With dynamic spacing
const resultWithDynamicSpacing = calculateAutoLayout(denseNodes, denseEdges, {
  dynamicSpacing: true,
  horizontalSpacing: 300,
  verticalSpacing: 200
})

console.log('\nWith dynamic spacing:')
resultWithDynamicSpacing.positions.forEach((pos, nodeId) => {
  console.log(`  ${nodeId}: (${pos.x}, ${pos.y})`)
})

// Demo 3: Vertical layout
console.log('\n=== Demo 3: Vertical Layout with Edge Optimization ===')

const verticalResult = calculateAutoLayout(complexNodes, complexEdges, {
  handleOrientation: 'vertical',
  minimizeEdgeCrossings: true,
  dynamicSpacing: true,
  horizontalSpacing: 300,
  verticalSpacing: 200
})

console.log('Vertical layout with optimization:')
verticalResult.positions.forEach((pos, nodeId) => {
  console.log(`  ${nodeId}: (${pos.x}, ${pos.y})`)
})

console.log('\n=== Summary ===')
console.log('✅ Edge crossing minimization implemented using barycenter heuristic')
console.log('✅ Dynamic spacing based on edge density')
console.log('✅ Support for both horizontal and vertical layouts')
console.log('✅ Backward compatible with existing autolayout options')
console.log('✅ Configurable iterations and spacing multipliers')
