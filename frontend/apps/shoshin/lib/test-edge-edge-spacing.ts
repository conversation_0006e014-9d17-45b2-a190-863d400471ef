import { type Edge, type Node } from "@xyflow/react";
import { calculateAutoLayout } from "./autoLayout";

// Helper functions for creating test nodes and edges
const createNode = (id: string, x: number = 0, y: number = 0): Node => ({
  id,
  type: "custom",
  position: { x, y },
  data: { label: id },
});

const createEdge = (source: string, target: string): Edge => ({
  id: `${source}-${target}`,
  source,
  target,
  type: "shoshin",
});

console.log("=== Edge-to-Edge Distance Test ===");

// Test scenario: Create a more complex graph where edges would cross or be very close
const testNodes: Node[] = [
  createNode("A", 0, 0),
  createNode("B", 100, 50),
  createNode("C", 50, 0),
  createNode("D", 150, 50),
  createNode("E", 25, 25),
  createNode("F", 125, 75),
];

const testEdges: Edge[] = [
  createEdge("A", "B"), // Diagonal edge
  createEdge("C", "D"), // Another diagonal edge that could cross or be close
  createEdge("E", "F"), // Third diagonal edge
];

console.log(
  "Testing edge-edge spacing with different source and target nodes...",
);

// Test with edge-edge spacing enabled
const resultWithSpacing = calculateAutoLayout(testNodes, testEdges, {
  edgeNodeSpacing: 20,
  edgeNodeCollisionIterations: 8,
  edgeEdgeSpacing: 10, // Minimum 10px between edges
  edgeEdgeCollisionIterations: 5,
  horizontalSpacing: 80, // Small spacing to force edges close together
  verticalSpacing: 40, // Small vertical spacing
  minimizeEdgeCrossings: true,
  dynamicSpacing: true,
});

console.log("Node positions with edge-edge spacing:");
resultWithSpacing.positions.forEach((pos, nodeId) => {
  console.log(`  ${nodeId}: (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)})`);
});

// Calculate the vertical distance between the two edges
const posA = resultWithSpacing.positions.get("A")!;
const posB = resultWithSpacing.positions.get("B")!;
const posC = resultWithSpacing.positions.get("C")!;
const posD = resultWithSpacing.positions.get("D")!;

// Calculate edge centers (approximate)
const edge1CenterY = (posA.y + posB.y) / 2;
const edge2CenterY = (posC.y + posD.y) / 2;
const edgeDistance = Math.abs(edge2CenterY - edge1CenterY);

console.log(`\nEdge distance analysis:`);
console.log(`  Edge A-B center Y: ${edge1CenterY.toFixed(1)}`);
console.log(`  Edge C-D center Y: ${edge2CenterY.toFixed(1)}`);
console.log(`  Distance between edges: ${edgeDistance.toFixed(1)}px`);
console.log(
  `  Minimum spacing requirement met: ${edgeDistance >= 10 ? "YES ✅" : "NO ❌"}`,
);

// Test with edge-edge spacing disabled for comparison
console.log("\n=== Comparison: Without Edge-Edge Spacing ===");

const resultWithoutSpacing = calculateAutoLayout(testNodes, testEdges, {
  edgeNodeSpacing: 20,
  edgeNodeCollisionIterations: 8,
  edgeEdgeSpacing: 0, // Disabled
  edgeEdgeCollisionIterations: 0,
  horizontalSpacing: 80,
  verticalSpacing: 40,
  minimizeEdgeCrossings: true,
  dynamicSpacing: true,
});

console.log("Node positions without edge-edge spacing:");
resultWithoutSpacing.positions.forEach((pos, nodeId) => {
  console.log(`  ${nodeId}: (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)})`);
});

const posA2 = resultWithoutSpacing.positions.get("A")!;
const posB2 = resultWithoutSpacing.positions.get("B")!;
const posC2 = resultWithoutSpacing.positions.get("C")!;
const posD2 = resultWithoutSpacing.positions.get("D")!;

const edge1CenterY2 = (posA2.y + posB2.y) / 2;
const edge2CenterY2 = (posC2.y + posD2.y) / 2;
const edgeDistance2 = Math.abs(edge2CenterY2 - edge1CenterY2);

console.log(`\nComparison edge distance: ${edgeDistance2.toFixed(1)}px`);
console.log(
  `Improvement with spacing: ${(edgeDistance - edgeDistance2).toFixed(1)}px`,
);

console.log("\n=== Edge-Edge Spacing Test Completed ===");
